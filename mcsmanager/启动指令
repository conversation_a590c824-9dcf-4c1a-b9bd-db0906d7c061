重启：systemctl restart mcsm-{daemon,web}.service

启动：systemctl start mcsm-{daemon,web}.service
systemctl start mcsm-daemon.service
systemctl start mcsm-web.service

停止：systemctl stop mcsm-{daemon,web}.service

禁用：systemctl disable mcsm-{daemon,web}.service

启用：systemctl enable mcsm-{daemon,web}.service




bash -c export PATH="$HOME/nodejs/bin:$PATH" && cd /home/<USER>/dev_pagent/mcsmanager && ./start-daemon.sh &

bash -c export PATH="$HOME/nodejs/bin:$PATH" && cd /home/<USER>/dev_pagent/mcsmanager && ./start-web.sh &