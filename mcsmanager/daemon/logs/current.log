[07/11 06:12:41] [INFO] All feature modules and permission firewalls have been initialized successfully
[07/11 06:12:41] [INFO] LANGUAGE: zh_cn
[07/11 06:12:41] [INFO] 欢迎使用 MCSManager 守护进程
[07/11 06:12:41] [INFO] 所有应用实例已加载，总计 1 个
[07/11 06:12:41] [INFO] ----------------------------
[07/11 06:12:41] [INFO] 守护进程现已成功启动
[07/11 06:12:41] [INFO] 参考文档：https://docs.mcsmanager.com/
[07/11 06:12:41] [INFO] 访问地址：http://<IP地址>:23335/ 或 ws://<IP地址>:23335
[07/11 06:12:41] [INFO] 配置文件：data/Config/global.json
[07/11 06:12:41] [INFO] 访问密钥：d494bb5350d84efd0697127394b4ed18eeb4381ccfaa8e0
[07/11 06:12:41] [INFO] 密钥作为唯一认证方式，请使用 MCSManager 面板的节点功能连接程序
[07/11 06:12:41] [INFO] 你可以使用 Ctrl+C 快捷键即可关闭程序
[07/11 06:12:41] [INFO] ----------------------------
[07/11 06:14:22] [INFO] 会话 vgNcxBhPsSDZWZwyAAAL(::ffff:127.0.0.1) 验证身份成功
[07/11 06:14:22] [WARN] Language change: zh_cn
[07/11 06:14:46] [INFO] 会话 WOyH6LFMfvjjG7NnAAAN(::ffff:************) 因长时间未验证身份而断开连接
[07/11 06:15:39] [INFO] 会话 Iq8_VTZYCDnaV-dLAAAP(::ffff:************) 因长时间未验证身份而断开连接
[07/11 06:16:15] [INFO] 会话 : 请求开启实例，模式为仿真终端
[07/11 06:16:15] [INFO] ----------------
[07/11 06:16:15] [INFO] 会话 : 请求开启实例.
[07/11 06:16:15] [INFO] 实例标识符: [e08f1d2bd7304466b534262419bdf6c2]
[07/11 06:16:15] [INFO] 启动命令: java -server -Dfile.encoding=UTF-8 -Duser.language=zh -Duser.country=CN -jar ${ProgramName} --installServer

[07/11 06:16:15] [INFO] PTY 路径: /home/<USER>/dev_pagent/mcsmanager/daemon/lib/pty_linux_x64
[07/11 06:16:15] [INFO] PTY 参数: -size 164,40 -coder utf8 -dir /home/<USER>/dev_pagent/1.20.1_Forge机械动力乐事女仆光影/forge-1.20.1-47.4.3-installer.jar -fifo /tmp/mcsmanager-instance-pipe/pipe-284777ee-bb7a-40ed-ae3d-396d21a36aab -cmd ["java","-server","-Dfile.encoding=UTF-8","-Duser.language=zh","-Duser.country=CN","-jar","${ProgramName}","--installServer\n"]
[07/11 06:16:15] [INFO] 工作目录: /home/<USER>/dev_pagent/1.20.1_Forge机械动力乐事女仆光影/forge-1.20.1-47.4.3-installer.jar
[07/11 06:16:15] [INFO] ----------------
[07/11 06:16:15] [ERROR] 实例e08f1d2bd7304466b534262419bdf6c2启动时错误:  StartupError: 实例启动失败，请检查启动命令，主机环境和配置文件等
    at PtyStartCommand.createProcess (/home/<USER>/dev_pagent/mcsmanager/daemon/app.js:1443:19)
    at process.processTicksAndRejections (node:internal/process/task_queues:95:5)
    at async PtyStartCommand.exec (/home/<USER>/dev_pagent/mcsmanager/daemon/app.js:1503:20)
    at async PresetCommandManager.execPreset (/home/<USER>/dev_pagent/mcsmanager/daemon/app.js:2262:16)
    at async Instance.execPreset (/home/<USER>/dev_pagent/mcsmanager/daemon/app.js:2106:20)
    at async RouterApp.<anonymous> (/home/<USER>/dev_pagent/mcsmanager/daemon/app.js:2685:13)
[07/11 06:16:15] [WARN] 会话 vgNcxBhPsSDZWZwyAAAL(::ffff:127.0.0.1)/instance/open 响应数据时异常:
 {
  instanceUuid: 'e08f1d2bd7304466b534262419bdf6c2',
  err: '实例启动失败，请检查启动命令，主机环境和配置文件等'
}
[07/11 06:18:27] [INFO] 会话 -XubHWE5TOvk08q8AAAV(::ffff:************) 因长时间未验证身份而断开连接
